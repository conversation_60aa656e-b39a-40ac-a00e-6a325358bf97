// Compression modal functionality
function createCompressionModal() {
  const modal = document.createElement('div');
  modal.id = 'compression-modal';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Google Sans', Roboto, Arial, sans-serif;
  `;

  const modalContent = document.createElement('div');
  modalContent.style.cssText = `
    background: #fff;
    border-radius: 8px;
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
    overflow: hidden;
  `;

  const header = document.createElement('div');
  header.style.cssText = `
    padding: 24px 24px 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  `;

  const title = document.createElement('h2');
  title.textContent = 'Compress Video';
  title.style.cssText = `
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #202124;
    display: flex;
    align-items: center;
  `;

  const closeButton = document.createElement('button');
  closeButton.innerHTML = '×';
  closeButton.style.cssText = `
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #5f6368;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  closeButton.onmouseover = () => closeButton.style.background = '#f1f3f4';
  closeButton.onmouseout = () => closeButton.style.background = 'none';

  header.appendChild(title);
  header.appendChild(closeButton);

  const content = document.createElement('div');
  content.style.cssText = `
    padding: 24px;
  `;

  const description = document.createElement('p');
  description.textContent = 'Choose compression quality:';
  description.style.cssText = `
    margin: 0 0 20px 0;
    color: #5f6368;
    font-size: 14px;
  `;

  const optionsContainer = document.createElement('div');
  optionsContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 12px;
  `;

  const compressionOptions = [
    { value: 'high', label: 'High Quality', description: 'Minimal compression, larger file size' },
    { value: 'medium', label: 'Medium Quality', description: 'Balanced compression and quality' },
    { value: 'low', label: 'Low Quality', description: 'Maximum compression, smaller file size' }
  ];

  let selectedOption = 'medium';

  compressionOptions.forEach(option => {
    const optionDiv = document.createElement('div');
    optionDiv.style.cssText = `
      padding: 16px;
      border: 2px solid #e8eaed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
    `;

    const radio = document.createElement('input');
    radio.type = 'radio';
    radio.name = 'compression';
    radio.value = option.value;
    radio.checked = option.value === selectedOption;
    radio.style.cssText = `
      margin-right: 12px;
      accent-color: #1a73e8;
    `;

    const labelContainer = document.createElement('div');
    labelContainer.style.cssText = `
      flex: 1;
    `;

    const label = document.createElement('div');
    label.textContent = option.label;
    label.style.cssText = `
      font-weight: 500;
      color: #202124;
      margin-bottom: 4px;
    `;

    const desc = document.createElement('div');
    desc.textContent = option.description;
    desc.style.cssText = `
      font-size: 12px;
      color: #5f6368;
    `;

    labelContainer.appendChild(label);
    labelContainer.appendChild(desc);
    optionDiv.appendChild(radio);
    optionDiv.appendChild(labelContainer);

    optionDiv.onclick = () => {
      selectedOption = option.value;
      document.querySelectorAll('input[name="compression"]').forEach(r => r.checked = false);
      radio.checked = true;
      updateOptionStyles();
    };

    optionDiv.onmouseover = () => {
      if (!radio.checked) {
        optionDiv.style.borderColor = '#dadce0';
        optionDiv.style.backgroundColor = '#f8f9fa';
      }
    };

    optionDiv.onmouseout = () => {
      if (!radio.checked) {
        optionDiv.style.borderColor = '#e8eaed';
        optionDiv.style.backgroundColor = 'transparent';
      }
    };

    optionsContainer.appendChild(optionDiv);
  });

  function updateOptionStyles() {
    document.querySelectorAll('#compression-modal [name="compression"]').forEach(radio => {
      const optionDiv = radio.closest('div');
      if (radio.checked) {
        optionDiv.style.borderColor = '#1a73e8';
        optionDiv.style.backgroundColor = '#e8f0fe';
      } else {
        optionDiv.style.borderColor = '#e8eaed';
        optionDiv.style.backgroundColor = 'transparent';
      }
    });
  }

  const buttonContainer = document.createElement('div');
  buttonContainer.style.cssText = `
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
  `;

  const cancelButton = document.createElement('button');
  cancelButton.textContent = 'Cancel';
  cancelButton.style.cssText = `
    padding: 10px 24px;
    border: none;
    background: none;
    color: #1a73e8;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  `;
  cancelButton.onmouseover = () => cancelButton.style.backgroundColor = '#f1f3f4';
  cancelButton.onmouseout = () => cancelButton.style.backgroundColor = 'transparent';

  const compressButton = document.createElement('button');
  compressButton.textContent = 'Compress';
  compressButton.style.cssText = `
    padding: 10px 24px;
    border: none;
    background: #1a73e8;
    color: white;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  `;
  compressButton.onmouseover = () => compressButton.style.backgroundColor = '#1557b0';
  compressButton.onmouseout = () => compressButton.style.backgroundColor = '#1a73e8';

  buttonContainer.appendChild(cancelButton);
  buttonContainer.appendChild(compressButton);

  content.appendChild(description);
  content.appendChild(optionsContainer);
  content.appendChild(buttonContainer);

  modalContent.appendChild(header);
  modalContent.appendChild(content);
  modal.appendChild(modalContent);

  // Event handlers
  const closeModal = () => {
    modal.remove();
  };

  closeButton.onclick = closeModal;
  cancelButton.onclick = closeModal;
  modal.onclick = (e) => {
    if (e.target === modal) closeModal();
  };

  compressButton.onclick = () => {
    const videoId = window.location.pathname.split('/')[2];
    if (!videoId) return;

    closeModal();

    chrome.runtime.sendMessage({
      action: "compressVideo",
      videoId: videoId,
      quality: selectedOption
    }, function(response) {
      if (response.success) {
        alert('Compression started!');
      } else {
        alert('Error: ' + response.message);
      }
    });
  };

  // Initialize styles
  setTimeout(updateOptionStyles, 0);

  return modal;
}

function addButton() {
  if (document.getElementById('compress-button')) {
    return;
  }

  const shareButton = document.querySelector('button[aria-label="Share"]');
  if (!shareButton) {
    setTimeout(addButton, 500); // Try again if the share button isn't loaded yet
    return;
  }

  const shareButtonWrapper = shareButton.closest('.DNAsC.G6iPcb');
  if (!shareButtonWrapper) {
    return;
  }

  const newButtonWrapper = document.createElement('div');
  newButtonWrapper.className = shareButtonWrapper.className;
  newButtonWrapper.setAttribute('jscontroller', shareButtonWrapper.getAttribute('jscontroller'));
  newButtonWrapper.setAttribute('jsaction', shareButtonWrapper.getAttribute('jsaction'));

  const tooltipWrapper = document.createElement('span');
  tooltipWrapper.setAttribute('data-is-tooltip-wrapper', 'true');

  const button = document.createElement('button');
  button.id = 'compress-button';
  button.className = shareButton.className;
  button.setAttribute('aria-label', 'Compress');
  button.setAttribute('data-tooltip-enabled', 'true');
  const tooltipId = 'tt-compress-' + Date.now();
  button.setAttribute('data-tooltip-id', tooltipId);
  button.setAttribute('data-tooltip-classes', 'hoXpM'); // Added missing attribute
  button.setAttribute('jscontroller', shareButton.getAttribute('jscontroller'));
  button.setAttribute('jsaction', shareButton.getAttribute('jsaction'));

  button.innerHTML = `
    <span class="OiePBf-zPjgPe pYTkkf-Bz112c-UHGRz"></span>
    <span class="RBHQF-ksKsZd"></span>
    <span class="pYTkkf-Bz112c-kBDsod-Rtc0Jf" aria-hidden="true">
      <span class="notranslate" aria-hidden="true">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 9L20 4M20 4H16M20 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 9L4 4M4 4H8M4 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M15 15L20 20M20 20H16M20 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 15L4 20M4 20H8M4 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    </span>
    <div class="pYTkkf-Bz112c-RLmnJb"></div>
  `;

  const tooltip = document.createElement('div');
  tooltip.className = 'ne2Ple-oshW8e-V67aGc';
  tooltip.id = tooltipId;
  tooltip.setAttribute('role', 'tooltip');
  tooltip.setAttribute('aria-hidden', 'true');
  tooltip.textContent = 'Compress';

  tooltipWrapper.appendChild(button);
  tooltipWrapper.appendChild(tooltip);
  newButtonWrapper.appendChild(tooltipWrapper);

  shareButtonWrapper.parentElement.insertBefore(newButtonWrapper, shareButtonWrapper);

  button.onclick = (e) => {
    e.stopPropagation();
    const modal = createCompressionModal();
    document.body.appendChild(modal);
  };
}

const observer = new MutationObserver(() => {
  addButton();
});

observer.observe(document.body, {
  childList: true,
  subtree: true
});

addButton();