function addButton() {
  if (document.getElementById('compress-button')) {
    return;
  }

  const shareButton = document.querySelector('button[aria-label="Share"]');
  if (!shareButton) {
    setTimeout(addButton, 500); // Try again if the share button isn't loaded yet
    return;
  }

  const shareButtonWrapper = shareButton.closest('.DNAsC.G6iPcb');
  if (!shareButtonWrapper) {
    return;
  }

  const newButtonWrapper = document.createElement('div');
  newButtonWrapper.className = shareButtonWrapper.className;
  newButtonWrapper.setAttribute('jscontroller', shareButtonWrapper.getAttribute('jscontroller'));
  newButtonWrapper.setAttribute('jsaction', shareButtonWrapper.getAttribute('jsaction'));

  const tooltipWrapper = document.createElement('span');
  tooltipWrapper.setAttribute('data-is-tooltip-wrapper', 'true');

  const button = document.createElement('button');
  button.id = 'compress-button';
  button.className = shareButton.className;
  button.setAttribute('aria-label', 'Compress');
  button.setAttribute('data-tooltip-enabled', 'true');
  const tooltipId = 'tt-compress-' + Date.now();
  button.setAttribute('data-tooltip-id', tooltipId);
  button.setAttribute('data-tooltip-classes', 'hoXpM'); // Added missing attribute
  button.setAttribute('jscontroller', shareButton.getAttribute('jscontroller'));
  button.setAttribute('jsaction', shareButton.getAttribute('jsaction'));


  button.innerHTML = `
    <span class="OiePBf-zPjgPe pYTkkf-Bz112c-UHGRz"></span>
    <span class="RBHQF-ksKsZd"></span>
    <span class="pYTkkf-Bz112c-kBDsod-Rtc0Jf" aria-hidden="true">
      <span class="notranslate" aria-hidden="true">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 9L20 4M20 4H16M20 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 9L4 4M4 4H8M4 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M15 15L20 20M20 20H16M20 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 15L4 20M4 20H8M4 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    </span>
    <div class="pYTkkf-Bz112c-RLmnJb"></div>
  `;

  const tooltip = document.createElement('div');
  tooltip.className = 'ne2Ple-oshW8e-V67aGc';
  tooltip.id = tooltipId;
  tooltip.setAttribute('role', 'tooltip');
  tooltip.setAttribute('aria-hidden', 'true');
  tooltip.textContent = 'Compress';

  tooltipWrapper.appendChild(button);
  tooltipWrapper.appendChild(tooltip);
  newButtonWrapper.appendChild(tooltipWrapper);

  shareButtonWrapper.parentElement.insertBefore(newButtonWrapper, shareButtonWrapper);

  button.onclick = (e) => {
    e.stopPropagation();
    const videoId = window.location.pathname.split('/')[2];
    if (!videoId) return;
    chrome.runtime.sendMessage({ action: "compressVideo", videoId: videoId }, function(response) {
      if (response.success) {
        alert('Compression started!');
      } else {
        alert('Error: ' + response.message);
      }
    });
  };
}

const observer = new MutationObserver((mutations) => {
  addButton();
});

observer.observe(document.body, {
  childList: true,
  subtree: true
});

addButton();